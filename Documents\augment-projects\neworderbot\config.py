"""
Configuration management for Discord automation bot
"""
import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from dotenv import load_dotenv

load_dotenv()

class BotConfig(BaseSettings):
    """Bot configuration settings"""
    
    # Discord settings
    discord_token: str
    guild_id: Optional[int] = None
    bot_prefix: str = "!"
    
    # Channel monitoring
    monitor_channels: List[int] = []
    
    # Logging
    log_level: str = "INFO"
    
    # Data storage
    data_dir: str = "./data"
    save_embeds: bool = True
    save_interactions: bool = True
    
    # Automation settings
    auto_respond: bool = False
    response_delay: float = 1.0
    max_retries: int = 3
    
    @validator('monitor_channels', pre=True)
    def parse_channels(cls, v):
        if isinstance(v, str):
            return [int(x.strip()) for x in v.split(',') if x.strip()]
        return v
    
    @validator('discord_token')
    def validate_token(cls, v):
        if not v or v == "your_bot_token_here":
            raise ValueError("Discord token must be set")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global config instance
config = BotConfig(
    discord_token=os.getenv("DISCORD_TOKEN", ""),
    guild_id=int(os.getenv("GUILD_ID", 0)) if os.getenv("GUILD_ID") else None,
    monitor_channels=os.getenv("MONITOR_CHANNELS", ""),
    bot_prefix=os.getenv("BOT_PREFIX", "!"),
    log_level=os.getenv("LOG_LEVEL", "INFO"),
    data_dir=os.getenv("DATA_DIR", "./data"),
    save_embeds=os.getenv("SAVE_EMBEDS", "true").lower() == "true",
    save_interactions=os.getenv("SAVE_INTERACTIONS", "true").lower() == "true",
    auto_respond=os.getenv("AUTO_RESPOND", "false").lower() == "true",
    response_delay=float(os.getenv("RESPONSE_DELAY", "1.0")),
    max_retries=int(os.getenv("MAX_RETRIES", "3"))
)
