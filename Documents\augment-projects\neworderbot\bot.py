"""
Main Discord automation bot
"""
import asyncio
import discord
from discord.ext import commands, tasks
from pathlib import Path
import json
from datetime import datetime
from typing import Dict, List, Optional

from config import config
from utils.logger import logger
from handlers.embed_handler import EmbedHandler
from handlers.interaction_handler import <PERSON>action<PERSON>andler
from handlers.data_processor import DataProcessor

class DiscordAutomationBot(commands.Bot):
    """Main bot class for Discord automation"""
    
    def __init__(self):
        # Set up intents
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True
        intents.guild_messages = True
        intents.guild_reactions = True
        
        super().__init__(
            command_prefix=config.bot_prefix,
            intents=intents,
            help_command=None
        )
        
        # Initialize handlers
        self.embed_handler = EmbedHandler(self)
        self.interaction_handler = InteractionHandler(self)
        self.data_processor = DataProcessor()
        
        # Data storage
        self.data_dir = Path(config.data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # Monitoring state
        self.monitored_channels = set(config.monitor_channels)
        self.processed_messages = set()
        
    async def setup_hook(self):
        """Called when the bot is starting up"""
        logger.info("Setting up bot...")
        
        # Add cogs/handlers
        await self.add_cog(self.embed_handler)
        await self.add_cog(self.interaction_handler)
        
        # Start background tasks
        if not self.monitor_channels.is_running():
            self.monitor_channels.start()
            
        logger.info("Bot setup complete")
    
    async def on_ready(self):
        """Called when bot is ready"""
        logger.info(f"Bot logged in as {self.user} (ID: {self.user.id})")
        logger.info(f"Monitoring {len(self.monitored_channels)} channels")
        logger.info("Bot is ready!")
    
    async def on_message(self, message):
        """Handle incoming messages"""
        # Ignore bot messages
        if message.author.bot:
            return
            
        # Process commands
        await self.process_commands(message)
        
        # Check if message is in monitored channels
        if message.channel.id in self.monitored_channels:
            await self.handle_monitored_message(message)
    
    async def handle_monitored_message(self, message):
        """Handle messages from monitored channels"""
        try:
            # Check for embeds
            if message.embeds:
                logger.info(f"Found {len(message.embeds)} embed(s) in message {message.id}")
                await self.embed_handler.process_embeds(message, message.embeds)
            
            # Check for components (buttons, dropdowns)
            if hasattr(message, 'components') and message.components:
                logger.info(f"Found interactive components in message {message.id}")
                await self.interaction_handler.handle_components(message)
                
        except Exception as e:
            logger.error(f"Error handling monitored message {message.id}: {e}")
    
    @tasks.loop(minutes=5)
    async def monitor_channels(self):
        """Background task to monitor channels for new content"""
        try:
            for channel_id in self.monitored_channels:
                channel = self.get_channel(channel_id)
                if not channel:
                    logger.warning(f"Could not find channel {channel_id}")
                    continue
                
                # Check recent messages for any we might have missed
                async for message in channel.history(limit=10):
                    if message.id not in self.processed_messages:
                        if message.embeds or (hasattr(message, 'components') and message.components):
                            await self.handle_monitored_message(message)
                            self.processed_messages.add(message.id)
                            
        except Exception as e:
            logger.error(f"Error in monitor_channels task: {e}")
    
    @monitor_channels.before_loop
    async def before_monitor_channels(self):
        """Wait for bot to be ready before starting monitoring"""
        await self.wait_until_ready()

# Bot commands
@commands.command(name="status")
async def status_command(ctx):
    """Check bot status"""
    embed = discord.Embed(
        title="Bot Status",
        color=discord.Color.green(),
        timestamp=datetime.utcnow()
    )
    embed.add_field(name="Channels Monitored", value=len(ctx.bot.monitored_channels), inline=True)
    embed.add_field(name="Messages Processed", value=len(ctx.bot.processed_messages), inline=True)
    embed.add_field(name="Uptime", value="Running", inline=True)
    
    await ctx.send(embed=embed)

@commands.command(name="add_channel")
@commands.has_permissions(administrator=True)
async def add_channel_command(ctx, channel_id: int):
    """Add a channel to monitoring list"""
    ctx.bot.monitored_channels.add(channel_id)
    await ctx.send(f"Added channel {channel_id} to monitoring list")

@commands.command(name="remove_channel")
@commands.has_permissions(administrator=True)
async def remove_channel_command(ctx, channel_id: int):
    """Remove a channel from monitoring list"""
    ctx.bot.monitored_channels.discard(channel_id)
    await ctx.send(f"Removed channel {channel_id} from monitoring list")

# Add commands to bot
async def main():
    """Main function to run the bot"""
    bot = DiscordAutomationBot()
    
    # Add commands
    bot.add_command(status_command)
    bot.add_command(add_channel_command)
    bot.add_command(remove_channel_command)
    
    try:
        await bot.start(config.discord_token)
    except Exception as e:
        logger.error(f"Failed to start bot: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
